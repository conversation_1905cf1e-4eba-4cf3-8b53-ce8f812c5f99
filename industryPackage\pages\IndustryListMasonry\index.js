import {
  singleChainStatisticApi,
  itypeApi,
  regionChainEntRankApi,
  singleChainKeyEntNumApi,
  mapHot,
  isPurchased
} from '../../../service/industryApi';
import {formatLargeNumber} from '../../../utils/util';
const app = getApp();
import Toast from '@vant/weapp/toast/toast.js';

Page({
  data: {
    // 头部统计数据
    statsDataAry: [], //用来传递
    statsData: [
      {
        value: '',
        unit: '家',
        label: '企业总量'
      },
      {
        value: '',
        unit: '个',
        label: '环节数'
      },
      {
        value: '',
        unit: '家',
        label: '上市企业'
      },
      {
        value: '',
        unit: '个',
        label: '专利数'
      }
    ],
    industryData: [
      {
        name: '上市企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        type: 'ssqy',
        count: 0
      },
      {
        name: '科技型企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent7.png',
        count: 0
      },
      {
        name: '专精特新企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent5.png',
        count: 0
      },
      {
        name: '高新技术企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent4.png',
        count: 0
      }
    ],
    treeData: [], // 产业链图谱
    vipVisible: false,
    title: '',
    provinceData: [], // 省数据源
    cityData: [], // 市数据源
    mapDatas: [], // 地图数据
    chain_code: '', //产业链
    purchased: false //是否购买
  },

  async onLoad(options) {
    //  region_code 这个参数暂时用不上
    let {chain_code, chain_name, purchased = ''} = options;
    if (!chain_code) {
      console.error('请传入产业链编码');
      return;
    }
    if (!purchased) {
      // 只要传了 false也是字符串为true 这里处理没有这个字段的情况
      const res = await isPurchased(chain_code);
      purchased = res.purchased;
    }
    // 获取沉浸式导航栏组件实例
    this.navbar = this.selectComponent('#navbar');
    this.setData({
      chain_name: decodeURIComponent(chain_name),
      chain_code,
      purchased: purchased === true || purchased === 'true'
    });
    this.getInit({
      chain_code
    });
  },

  onShow() {},

  /**
   * 初始化页面数据
   * @param {Object} params - 参数对象
   * @param {string} params.chain_code - 产业链代码
   */
  async getInit({chain_code}) {
    try {
      app.showLoading('加载中');

      // 并行请求所有API数据
      const apiResults = await this._fetchAllApiData(chain_code);

      // 处理并格式化数据
      const formattedData = this._processApiResults(apiResults);

      // 更新页面数据
      this._updatePageData(formattedData);
    } catch (error) {
      console.error('初始化数据失败:', error);
      app.showToast('数据加载失败，请重试', 'none', 2000);
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 并行获取所有API数据
   * @param {string} chain_code - 产业链代码
   * @returns {Promise<Object>} API结果对象
   */
  async _fetchAllApiData(chain_code) {
    const [
      statisticData,
      industryTypeData,
      provinceRankData,
      cityRankData,
      keyEntNumData,
      mapHotData
    ] = await Promise.all([
      singleChainStatisticApi({
        chain_code,
        chain_type: 1,
        region_type: 0
      }),
      itypeApi({
        chain_code
      }),
      regionChainEntRankApi({
        chain_code,
        rank_type: 1,
        top: 10
      }),
      regionChainEntRankApi({
        chain_code,
        rank_type: 2,
        top: 10
      }),
      singleChainKeyEntNumApi({
        chain_code
      }),
      mapHot({
        chain_code
      })
    ]);

    return {
      statisticData,
      industryTypeData,
      provinceRankData,
      cityRankData,
      keyEntNumData,
      mapHotData
    };
  },

  /**
   * 处理API返回的数据
   * @param {Object} apiResults - API结果对象
   * @returns {Object} 格式化后的数据
   */
  _processApiResults(apiResults) {
    const {
      statisticData,
      industryTypeData,
      provinceRankData,
      cityRankData,
      keyEntNumData,
      mapHotData
    } = apiResults;

    return {
      statsData: this._formatStatsData(statisticData),
      statsDataAry: this._extractStatsArray(statisticData),
      treeData: this._formatTreeData(industryTypeData),
      provinceData: this._formatRegionData(provinceRankData, 'province'),
      cityData: this._formatRegionData(cityRankData, 'city'),
      mapDatas: this._formatMapData(mapHotData),
      industryData: this._formatIndustryData(keyEntNumData)
    };
  },

  /**
   * 格式化统计数据
   * @param {Object} data - 统计数据
   * @returns {Array} 格式化后的统计数据
   */
  _formatStatsData(data) {
    const {statsData} = this.data;
    const formattedStats = [...statsData];

    formattedStats[0].value = formatLargeNumber(data.ent_cnt || 0);
    formattedStats[1].value = formatLargeNumber(data.chain_cnt || 0);
    formattedStats[2].value = formatLargeNumber(data.listed_ent_cnt || 0);
    formattedStats[3].value = formatLargeNumber(data.patent_cnt || 0);

    return formattedStats;
  },

  /**
   * 提取统计数据数组
   * @param {Object} data - 统计数据
   * @returns {Array} 统计数据数组
   */
  _extractStatsArray(data) {
    return [
      data.ent_cnt || 0,
      data.chain_cnt || 0,
      data.listed_ent_cnt || 0,
      data.patent_cnt || 0
    ];
  },

  /**
   * 格式化树形数据 - 转换为 HOT_INDUSTRY_DATA 格式
   * @param {Object} data - 产业类型数据
   * @returns {Array} 格式化后的树形数据
   */
  _formatTreeData(data) {
    if (!data || !data.name) {
      return [];
    }

    // 转换为 HOT_INDUSTRY_DATA 格式
    return [
      {
        name: data.name,
        children:
          data.childs?.map(firstLevel => ({
            name: firstLevel.name,
            children:
              firstLevel.childs?.map(secondLevel => ({
                name: secondLevel.name
              })) || []
          })) || []
      }
    ];
  },

  /**
   * 生成唯一的 code 标识符
   * @param {string} name - 节点名称
   * @param {number} index - 节点索引
   * @param {string} parentName - 父节点名称（可选）
   * @returns {string} 生成的 code
   */
  _generateCode(name, index, parentName = '') {
    // 移除特殊字符，转换为小写，用下划线连接
    const cleanName = name
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 保留中文、英文、数字
      .toLowerCase();

    // 如果有父节点名称，加上父节点前缀
    const prefix = parentName
      ? parentName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').toLowerCase() + '_'
      : '';

    return `${prefix}${cleanName}_${index}`;
  },

  /**
   * 格式化区域数据
   * @param {Array} data - 区域数据
   * @param {string} regionType - 区域类型 ('province' | 'city')
   * @returns {Array} 格式化后的区域数据
   */
  _formatRegionData(data, regionType) {
    if (!Array.isArray(data)) {
      return [];
    }

    return data.map(item => ({
      name: item.region_name,
      value: item.total,
      region: regionType,
      ...item
    }));
  },

  /**
   * 格式化地图数据
   * @param {Object} data - 地图数据
   * @returns {Array} 格式化后的地图数据
   */
  _formatMapData(data) {
    if (!data || !Array.isArray(data.data_list)) {
      return [];
    }

    return data.data_list.map(item => ({
      name: item.region_name,
      value: item.count
    }));
  },

  /**
   * 格式化产业数据
   * @param {Object} data - 企业数量数据
   * @returns {Array} 格式化后的产业数据
   */
  _formatIndustryData(data) {
    const {industryData} = this.data;
    const formattedIndustry = [...industryData];

    formattedIndustry[0].count = data.listed_ent_cnt || 0;
    formattedIndustry[1].count = data.technology_ent_cnt || 0;
    formattedIndustry[2].count = data.specialized_ent_cnt || 0;
    formattedIndustry[3].count = data.high_tech_ent_cnt || 0;

    return formattedIndustry;
  },

  /**
   * 更新页面数据
   * @param {Object} data - 格式化后的数据
   */
  _updatePageData(data) {
    this.setData(data);
  },

  // 点击产业概况卡片
  onStatsClick(e) {
    const {label} = e.detail;
    const {chain_code, purchased, chain_name} = this.data;
    // 不需要验证
    if (label == '企业总量' || label == '查看企业名单') {
      //跳转到列表页面
      app.route(
        this,
        `/industryPackage/pages/businessList/index?isIndustryMap=true&filter_code=${chain_code}&purchased=${purchased}&filter_name=${decodeURIComponent(
          chain_name
        )}&category=chainMap`
      );
      return;
    }
    if (label == '上市企业') {
      //跳转到产业链图谱页面
      const bol = this.onIsPurchased();
      if (!bol) return;
      app.route(
        this,
        `/industryPackage/pages/businessList/index?isIndustryMap=true&filter_name=${decodeURIComponent(
          chain_name
        )}&title=${encodeURIComponent(
          '上市企业'
        )}&filter_code=${chain_code}&category=chainMap`
      );
      return;
    }
  },

  // 企业类型卡片点击事件
  onEnterpriseItemClick(e) {
    const {item} = e.detail;
    const {chain_code, chain_name} = this.data;
    const bol = this.onIsPurchased();
    if (!bol) return;
    if (item.type) {
      //跳转到单独的上市企业页面
      app.route(
        this,
        `/industryPackage/pages/listedCompany/index?filter_code=${chain_code}&filter_name=${decodeURIComponent(
          chain_name
        )}`
      );
      return;
    }
    // 跳转到列表页面
    app.route(
      this,
      `/industryPackage/pages/businessList/index?isIndustryMap=true&title=${encodeURIComponent(
        item.name
      )}&filter_code=${chain_code}&filter_name=${decodeURIComponent(
        chain_name
      )}&category=chainMap`
    );
  },

  // 地图类型切换事件
  onMapTypeChange(e) {
    const fn = e.detail.fn;
    const bl = this.onIsPurchased();
    if (bl) {
      fn();
    }
  },
  // 去全屏产业图谱
  onTuClick() {
    const {chain_code, chain_name, purchased} = this.data;
    app.route(
      this,
      `/industryPackage/pages/IndustryMapMasonry/index?chain_code=${chain_code}&purchased=${purchased}&type=chainMap&chain_name=${encodeURIComponent(
        chain_name
      )}`
    );
  },
  // 产业分析
  goDetail(e) {
    const {fn} = e.detail;
    const bl = this.onIsPurchased();
    if (!bl) return;
    fn();
  },
  onVipClose() {
    this.setData({
      vipVisible: false
    });
  },
  // 点击立即申请
  onVipSure() {
    //  向后端发送请求
    // app.showToast(
    //   '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢'
    // );
    Toast({
      message: '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢',
      duration: 3000,
      selector: '#van-toast'
    });
    this.setData({
      vipVisible: false
    });
  },
  // 是否购买 不是就阻止继续往下进行 弹窗
  onIsPurchased() {
    const {purchased} = this.data;
    if (!purchased) {
      this.setData({
        vipVisible: true
      });
      return false;
    }
    return true;
  }
});
