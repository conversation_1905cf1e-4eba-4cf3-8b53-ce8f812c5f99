import {chain} from '../../../service/api';
import {preventActive} from '../../../utils/util.js';
import {getHeight} from '../../../utils/height.js';
import {collect} from '../../../utils/mixin/collect';
import Toast from '@vant/weapp/toast/toast.js';

const app = getApp();
Component({
  properties: {
    // 固定文本标题（动态，如"产业类型"）
    fixedTitle: {
      type: String,
      value: '',
      observer(newVal) {
        // 当 fixedTitle 变化时，重新更新下拉菜单配置
        this.updateDropDownConfig();
      }
    },
    chain_name: {
      type: String,
      value: ''
    },
    // 是否显示地图模式按钮（动态）
    showMapMode: {
      type: Boolean,
      value: true
    },

    // 弹窗模式（新增）
    popupMode: {
      type: Boolean,
      value: false,
      observer(newVal, oldVal) {
        // 当弹窗模式变化时，重新计算高度
        if (newVal !== oldVal && this.data.initialized) {
          setTimeout(() => {
            this.handleHeight();
          }, 200);
        }
      }
    },

    // 弹窗顶部占位高度（rpx单位，用于兼容不同的弹窗布局）
    popupTopOffset: {
      type: Number,
      value: 0 // 默认0，表示没有额外的顶部占位
    },
    // 下拉菜单配置（主要配置项）
    dropDownMenuConfig: {
      type: Array,
      value: ['region', 'industry', 'filter'], // 默认配置：地区、产业链、更多筛选
      observer() {
        this.updateDropDownConfig();
      }
    },

    // 外部筛选状态（用于回显外部的筛选条件）
    externalFilterState: {
      type: Object,
      value: null,
      observer(newVal) {
        if (newVal && this.data.initialized) {
          this.applyExternalFilterState(newVal);
        }
      }
    },
    category: {
      type: String,
      value: '' //区分热点和经典产业链
    },
    // 是否是产业图谱--渲染得样式是不一样的
    isIndustryMap: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          this.setData({
            isCheckVip: 'masonryVip'
          });
        }
      }
    },
    //产业图谱是否购买-进到这里默认是购买的
    purchased: {
      type: Boolean,
      value: true
    },
    isCheckVip: {
      // 这里应该有三种 1.vip 2.masonryVip 3.不需要vip
      type: String,
      value: ''
    },

    // 是否使用产业链专用选择器
    useIndustrySingleSelect: {
      type: Boolean,
      value: false
    },
    // 产业链代码（用于IndustrySingleSelect获取数据）
    chain_code: {
      type: String,
      value: ''
    }
  },

  data: {
    company_num: 0,
    requestData: [],
    initialized: false, // 标记组件是否已初始化

    // 写死的参数
    requestFunction: chain.chainDetail, // 请求函数
    requestParams: {
      industrial_list: []
    }, // 请求参数

    industrial_list: [], // 产业链列表

    // 联系方式
    showContact: false,
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    locationMap: {},
    activeEntId: '', // 当前点击企业的id
    isLogin: false,
    computedHeight: 600, // 列表容器高度
    vipVisible: false, // VIP弹窗显示状态
    masonryVipVisible: false, // 未购买弹窗

    // 动态下拉菜单配置
    computedDropDownMenuConfig: [],
    dropDownMenuTitle: [], // 根据配置自动生成

    // 存储外部筛选状态
    externalFilterState: null
  },

  lifetimes: {
    attached() {
      this.setData({
        isLogin: app.isLogin()
      });
      // 先更新配置，再初始化数据
      this.updateDropDownConfig();
    },
    ready() {
      // 标记初始化完成
      this.setData({
        initialized: true
      });

      // 如果有外部筛选状态，先应用它
      if (this.properties.externalFilterState) {
        this.applyExternalFilterState(this.properties.externalFilterState);
      }

      // 手动触发组件加载数据
      this.selectComponent('#refresh-scroll').loadData();
      this.handleHeight();
    }
  },

  methods: {
    // 更新下拉菜单配置
    updateDropDownConfig() {
      const {dropDownMenuConfig, fixedTitle} = this.properties;

      // 根据配置生成对应的标题
      const titleMap = {
        region: '全国',
        industry: fixedTitle || '产业类型', // 使用外部传入的 fixedTitle
        filter: '更多筛选'
      };

      const finalTitles = dropDownMenuConfig.map(
        config => titleMap[config] || '更多筛选'
      );
      this.setData({
        computedDropDownMenuConfig: dropDownMenuConfig,
        dropDownMenuTitle: finalTitles
      });
    },
    // 处理组件数据变化
    onDataChange(e) {
      let {list, total} = e.detail;
      // 未购买产业链图谱，只显示前10条数据（障眼法） 到时候看这里后端处理不 现在这样处理有个问题就一直上拉可以加载更多
      list = this.data.purchased ? list : (list || []).slice(0, 10);
      this.setData({
        requestData: list,
        company_num: total || 0
      });

      // 向父组件传递数据变化事件
      this.triggerEvent('datachange', {
        list,
        total: total || 0
      });
    },

    // 地图导航
    goMap() {
      const {locationMap} = this.data;
      wx.openLocation(locationMap);
    },

    // 关闭地址弹窗
    onCloseAddress() {
      this.setData({
        showAddress: false
      });
    },

    // ComCard 组件事件处理
    onCardAction(e) {
      const {type, data, index} = e.detail;
      let that = this;

      preventActive(this, async () => {
        switch (type) {
          case 'collect':
            // 收藏企业
            const comDetail = {
              ...data
            };
            collect(that, comDetail, 'requestData', index);
            break;
          case 'relation':
            // 联系方式
            this.setData({
              activeEntId: data.ent_id,
              showContact: true
            });
            break;
          case 'official':
            // 官网跳转
            if (data.official_website) {
              wx.navigateTo({
                url: `/webview/webview?url=${encodeURIComponent(
                  data.official_website
                )}`
              });
            }
            break;
          case 'site':
            // 地址查看
            if (data.location && data.location.lat && data.location.lon) {
              this.setData({
                location: {
                  lat: +data.location.lat,
                  lon: +data.location.lon
                },
                locationTxt: data.register_address,
                addmarkers: [
                  {
                    id: 1,
                    latitude: +data.location.lat,
                    longitude: +data.location.lon,
                    iconPath:
                      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
                    width: 20,
                    height: 20
                  }
                ],
                showAddress: true,
                locationMap: {
                  latitude: +data.location.lat, // 维度
                  longitude: +data.location.lon, // 经度
                  name: data.register_address, // 目的地定位名称
                  scale: 15, // 缩放比例
                  address: data.register_address // 导航详细地址
                }
              });
            } else {
              wx.showToast({
                title: '暂无地址信息',
                icon: 'none'
              });
            }
            break;
        }
      });
    },

    // 点击企业标题
    handleTit(e) {
      const {ent_id} = e.detail;
      if (ent_id) {
        wx.navigateTo({
          url: `/companyPackage/pages/companyDetail/companyDetail?entId=${ent_id}`
        });
      }
    },

    // 动态获取蒙层高度
    handleHeight() {
      const that = this;

      // 如果是弹窗模式，使用不同的计算方式
      if (this.properties.popupMode) {
        this.calculatePopupHeight();
      } else {
        // 普通模式的高度计算
        getHeight(that, '.business-list-component .page_head', data => {
          this.setData({
            computedHeight: data.screeHeight - data.res[0].height,
            isLogin: app.isLogin()
          });
        });
      }
    },
    // 判断是否用了原生导航栏

    // 弹窗模式下的高度计算
    calculatePopupHeight() {
      // 延迟执行，确保DOM渲染完成
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);
        // 获取多个元素的高度
        query.select('.card-box').boundingClientRect(); // 组件容器高度
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        query.exec(res => {
          try {
            const systemInfo = wx.getSystemInfoSync();
            // 这个列表会有没有导航栏使用的情况 高度应该取screeHeight
            const windowHeight = Object.keys(currentPage.data).includes(
              'bottomTabs1'
            )
              ? systemInfo.screenHeight
              : systemInfo.windowHeight;
            this.setData({
              computedHeight: windowHeight - res[0].top,
              isLogin: app.isLogin()
            });
          } catch (error) {
            console.error('弹窗模式高度计算失败:', error);
            this.setFallbackHeight();
          }
        });
      });
    },
    // 设置备用高度
    setFallbackHeight() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const fallbackHeight = Math.max(300, systemInfo.screenHeight * 0.6);

        this.setData({
          computedHeight: fallbackHeight,
          isLogin: app.isLogin()
        });
      } catch (error) {
        // 最后的备用方案
        this.setData({
          computedHeight: 400,
          isLogin: app.isLogin()
        });
      }
    },

    // 顶部筛选--------这里逻辑有点问题
    onFlitter(e) {
      let {dropDownMenuTitle, externalFilterState, category, isIndustryMap} =
        this.data;
      const obj = e.detail;
      let tempRequestParams = {};

      // 更新下拉菜单标题
      if (obj.name1) dropDownMenuTitle[0] = obj.name1;
      if (obj.name2) dropDownMenuTitle[1] = obj.name2;
      // 清理临时字段并合并参数
      const {
        name1,
        name2,
        isFilter,
        regionData,
        industrial_list, //这里返回的是字符串
        area_code_list,
        chainType,
        ...filterParams
      } = obj;
      tempRequestParams = {
        ...filterParams
      };
      if (chainType === 'classic') {
        // 经典用这个字段
        tempRequestParams.classic_industry_code_list = industrial_list
          ? [industrial_list]
          : [];
      } else if (['chainMap', 'hot'].includes(chainType)) {
        tempRequestParams.industrial_list = industrial_list
          ? [industrial_list]
          : [];
      } else if (
        // 特殊情况_产业图谱 热点 如果没选 一进来有值得情况
        (isIndustryMap || category === 'hot' || category === 'chainMap') &&
        externalFilterState?.industrial_list?.code
      ) {
        tempRequestParams.industrial_list = [
          externalFilterState.industrial_list.code
        ];
      } else if (
        category === 'classic' &&
        externalFilterState?.classic_industry_code_list?.code
      ) {
        // 经典
        tempRequestParams.classic_industry_code_list = [
          externalFilterState.classic_industry_code_list.code
        ];
      }

      tempRequestParams.area_code_list =
        area_code_list && area_code_list !== 'All' ? [area_code_list] : [];

      this.setData(
        {
          requestParams: tempRequestParams,
          dropDownMenuTitle
        },
        () => {
          // 触发组件重新加载数据
          this.selectComponent('#refresh-scroll').reload();
        }
      );
    },

    // VIP弹窗
    vipPop(val) {
      if (val?.type === 'close') {
        this.setData({
          vipVisible: false
        });
        return;
      }
      this.setData({
        vipVisible: val
      });
    },

    // 应用外部筛选状态（用于回显外部的筛选条件）
    applyExternalFilterState(externalState) {
      if (!externalState) return;

      // console.log('BusinessListComponent 应用外部筛选状态:', externalState);

      const {
        regionData,
        industrial_list,
        filterParams,
        classic_industry_code_list,
        originalCode, // 产业链图谱 进来-跳转图谱取最原始的（排除子节点）
        originalName
      } = externalState;
      let {requestParams, dropDownMenuTitle, dropDownMenuConfig} = this.data;

      // 处理地区数据
      if (regionData && regionData.code && regionData.code !== 'All') {
        dropDownMenuTitle[0] = regionData.name || '全国';
        requestParams.area_code_list = [regionData.code];
      }

      // 处理热点和图谱产业链数据
      if (industrial_list && industrial_list.code) {
        const chainItem = industrial_list;
        if (chainItem && chainItem.code) {
          requestParams.industrial_list = [chainItem.code];
        }
        // 如果是产业链图谱 这个不显示
        if (
          dropDownMenuTitle.length !== 1 &&
          dropDownMenuConfig.includes('industry')
        ) {
          dropDownMenuTitle[1] = chainItem.name || '产业类型';
        }
      }
      // 处理经典产业链数据
      if (classic_industry_code_list && classic_industry_code_list.code) {
        const chainItem = classic_industry_code_list;
        if (chainItem && chainItem.code) {
          requestParams.classic_industry_code_list = [chainItem.code];
          if (
            dropDownMenuTitle.length !== 1 &&
            dropDownMenuConfig.includes('industry')
          ) {
            dropDownMenuTitle[1] = chainItem.name || '产业类型';
          }
        }
      }

      // 处理其他筛选参数（如果有的话）
      if (filterParams) {
        // 排除一些不需要的字段
        const {name1, name2, ...otherParams} = filterParams;
        requestParams = {
          ...requestParams,
          ...otherParams
        };
      }
      if (originalCode) {
        delete externalState.originalCode;
        delete externalState.originalName;
      }
      // console.log('333333', externalState);
      // 更新组件状态，包括传递给DropDownMenu的外部状态
      this.setData({
        requestParams,
        dropDownMenuTitle,
        externalFilterState: externalState,
        originalCode,
        originalName
      });
    },
    // 地图模式
    goMapMode() {
      const {
        isIndustryMap,
        externalFilterState,
        category,
        originalCode,
        originalName
      } = this.data;
      const name =
        category !== 'classic'
          ? externalFilterState?.industrial_list?.name
          : externalFilterState?.classic_industry_code_list?.name;

      const code =
        category !== 'classic'
          ? externalFilterState?.industrial_list?.code
          : externalFilterState?.classic_industry_code_list?.code;
      if (!this.data.purchased) {
        this.onNotPurchased();
        return;
      }
      let routeStr = `/industryPackage/pages/businessMapList/index?category=${category}`;
      if (isIndustryMap) {
        // 这是父亲节点 传递进去获取下拉弹窗
        routeStr = routeStr + `&isIndustryMap=true`;
      }
      if (originalCode) {
        routeStr =
          routeStr + `&chain_code=${originalCode}&chain_name=${originalName}`;
      } else {
        routeStr = routeStr + `&chain_code=${code}&chain_name=${name}`;
      }
      app.route(this, routeStr);
    },
    // 下拉筛选未购买触发的方法
    onNotPurchased() {
      this.setData({
        masonryVipVisible: true
      });
    },

    onVipClose() {
      this.setData({
        masonryVipVisible: false
      });
    },
    onSend() {
      this.onVipClose();
      // app.showToast(
      //   '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢'
      // );
      Toast({
        message: '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢',
        duration: 3000,
        selector: '#van-toast'
      });
    }
  }
});
