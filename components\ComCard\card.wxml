<view class="search-card hcard">
  <view class="card-boxs">
    <block>
      <!-- && index <= 99 -->
      <view wx:if="{{isBangDan }}" class="bangdan_logo">
        <view class="left">
          <!-- 背景 -->
          <image
            class="bd_bg"
            wx:if="{{objData.sort==1}}"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/one_ico.png"
          ></image>
          <image
            class="bd_bg"
            wx:if="{{objData.sort==2}}"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/two_ico.png"
          ></image>
          <image
            class="bd_bg"
            wx:if="{{objData.sort==3}}"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/three_ico.png"
          ></image>
          <image
            class="bd_bg"
            wx:if="{{objData.sort != 1 &&objData.sort != 2&&objData.sort != 3}}"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/companyPackageImg/more_ico.png"
          ></image>
          <text
            wx:if="{{objData.sort === 1}}"
            style="color: #E72410;"
            >{{objData.sort}}</text
          >
          <text
            wx:if="{{objData.sort === 2}}"
            style="color: #FF6A2A;"
            >{{objData.sort}}</text
          >
          <text
            wx:if="{{objData.sort === 3}}"
            style="color: #FBA61E;"
            >{{objData.sort}}</text
          >
          <text
            wx:if="{{objData.sort != 1 &&objData.sort != 2&&objData.sort != 3}}"
            style="color: #9B9EAC;"
            >{{objData.sort}}</text
          >
        </view>
        <image
          src="{{objData.src=='' || objData.logo=='' || objData.ent_logo=='' ?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':objData.src || objData.logo || objData.ent_logo }}"
          class="img"
          binderror="errorFunction"
        ></image>
      </view>
      <view
        wx:else
        class="card-logo"
        bindtap="goDetail"
        data-item="{{objData}}"
      >
        <!-- <image src="{{objData.src || objData.logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesen.png'}}" mode="aspectFit"></image> -->
        <image
          src="{{objData.src=='' || objData.logo=='' || objData.ent_logo=='' ?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':objData.src || objData.logo || objData.ent_logo }}"
          class="img"
          binderror="errorFunction"
        ></image>
      </view>
    </block>
    <view class="card-head">
      <view class="card_h">
        <!-- 企业名字需要单独处理 晚点来搞 -->
        <view class="card_h_l" bindtap="goDetail" data-item="{{objData}}">
          <block wx:if="{{objData.highlightedTitle}}">
            <view
              wx:for="{{objData.highlightedTitle}}"
              wx:key="index"
              class="text {{item.highlight&&'activetext'}}"
              >{{item.text}}</view
            >
          </block>
          <block wx:elif="{{objData.isCapital}}">
            <view
              wx:for="{{objData.ent_name}}"
              wx:key="index"
              class="text {{item==objData.heightKey&&'activetexts'}}"
              >{{item}}</view
            >
          </block>
          <block wx:else>
            <view
              wx:for="{{objData.ent_name}}"
              wx:key="index"
              class="text {{item==objData.heightKey&&'activetext'}}"
              >{{item}}</view
            >
          </block>
        </view>
        <view
          class="card_h_r"
          catchtap="collect"
          data-item="{{objData}}"
          wx:if="{{btn=='collect'}}"
        >
          <!-- 后面根据具体传进来的字段判断 -->
          <view class="card_h_r_img">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
              wx:if="{{!objData.collect}}"
            ></image>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
              wx:if="{{objData.collect}}"
            ></image>
          </view>
          <!-- <text>收藏</text> -->
        </view>
        <view
          wx:if="{{btn=='add'}}"
          class="card_h_r_2"
          catchtap="add"
          data-item="{{objData}}"
        >
          <view></view>
          <text>添加</text>
        </view>
        <view
          wx:if="{{btn=='monitoring'}}"
          class="card_h_r_3"
          catchtap="add"
          data-item="{{objData}}"
        >
          <text>监控</text>
        </view>
      </view>
      <view class="card_tag">
        <view class="card_tag_box">
          <block
            wx:for="{{tags}}"
            wx:key="idx"
            wx:for-item="tag"
            wx:for-index="idx"
          >
            <text
              class="card_tag_i {{tag.tagColor}}"
              wx:if="{{idx<3}}"
              >{{tag.tagName}}</text
            >
          </block>
        </view>
      </view>
    </view>
  </view>
  <view class="card_c" bindtap="goDetail" data-item="{{objData}}">
    <view class="card_c_i ">
      <text class="name">法人代表人</text>
      <text class="cont contblue">{{objData.legal_person}}</text>
    </view>
    <view class="card_c_i card_c_is  zj">
      <text class="name">注册资本</text>
      <text class="cont" wx:if="{{objData.reg_capital}}"
        >{{objData.reg_capital || '--' }}万</text
      >
      <text class="cont" wx:if="{{objData.reg_cap}}"
        >{{objData.reg_cap || '--' }}元</text
      >
    </view>
    <view class="card_c_i">
      <text class="name">成立日期</text>
      <text
        class="cont"
        wx:if="{{objData.register_date}}"
        >{{objData.register_date || '--' }}</text
      >
      <text
        class="cont"
        wx:if="{{objData.es_date}}"
        >{{objData.es_date || '--' }}</text
      >
    </view>
  </view>
  <view class="card_ico">
    <view class="card_ico_i" catchtap="relation" data-item="{{objData}}">
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 联系方式 </view>
    </view>
    <view
      class="card_ico_i"
      catchtap="official"
      data-item="{{objData}}"
      wx:if="{{objData.official_website || objData.website}}"
    >
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 官网 </view>
    </view>
    <view
      class="card_ico_i"
      catchtap="site"
      data-item="{{objData}}"
      wx:if="{{objData.location.lon!=''&&objData.location.lat!=''}}"
    >
      <view class="card_ico_i_img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
        ></image>
      </view>
      <view> 地址 </view>
    </view>
  </view>
</view>
