<immersive-navbar id="navbar" title="{{chain_name}}" show-back-btn="{{true}}">
  <view slot="content" class="content">
    <!-- 产业概况 -->
    <head-component
      title="产业概况"
      stats-data="{{statsData}}"
      bind:buttonClick="onStatsClick"
      bindstats="onStatsClick"
      type="masonry"
      bgImage="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/nav/{{chain_code}}_P.png"
    />

    <view class="box">
      <!-- 产业图谱 -->
      <CardLayout title="产业链图谱" type="masonry">
        <view slot="left" class="tu_left" bindtap="onTuClick">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tree_scall.png"
            class="img"
          ></image>
          全屏
        </view>
        <view slot="content">
          <HorizontalTree treeData="{{treeData}}" />
        </view>
      </CardLayout>
      <!-- 区域分布 -->
      <Area
        bind:mapTypeChange="onMapTypeChange"
        maxTableRows="{{8}}"
        showTypeSwitch="{{true}}"
        indutryType="chainMap"
        chianCode="{{chain_code}}"
        chianName="{{chain_name}}"
      />
      <!-- 重点企业 -->
      <EnterpriseList
        bind:itemClick="onEnterpriseItemClick"
        source="{{industryData}}"
      />
      <!-- 产业分析 -->
      <IndustryAnalysis
        bindGo="goDetail"
        chain_code="{{chain_code}}"
        chain_name="{{chain_name}}"
      />
    </view>
  </view>
</immersive-navbar>

<!-- 全局弹窗 -->
<VipPop
  visible="{{vipVisible}}"
  bindclose="onVipClose"
  chain_code="{{chain_code}}"
  chain_name="{{chain_name}}"
  bindsend="onVipSure"
/>
<van-toast id="van-toast" />
