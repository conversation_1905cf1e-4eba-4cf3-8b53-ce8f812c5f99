<view
  style="height:{{wrapHeight}};"
  class="searWrap {{type=='chainMap' && 'industrial'}}"
>
  <!-- 左侧筛选区域 -->
  <scroll-view
    scroll-y
    style="height:{{wrapHeight}};"
    scroll-with-animation
    class="searWrap-r"
  >
    <view style="height: auto;">
      <view class="searL" wx:for="{{leftList}}" wx:key="index">
        <view
          class="{{item.isActive ? 'active' : ''}} tit"
          bindtap="onLeftItemClick"
          data-item="{{item}}"
          data-index="{{index}}"
        >
          {{item.title}}
          <text></text>
        </view>
      </view>
      <view style="height: 100rpx;"></view>
    </view>
  </scroll-view>

  <!-- 右侧内容区域 -->
  <view style="height:{{wrapHeight}};" class="box_wrap">
    <scroll-view
      scroll-y
      style="height:{{wrapHeight}};"
      scroll-with-animation
      class="searWrap-l"
      scroll-into-view="{{idName}}"
    >
      <view class="content">
        <!-- 固定内容 -->
        <view class="fixed_time" id="top-anchor">数据更新：{{date}}</view>
        <view class="three_card">
          <view class="fixed-item" wx:for="{{rightFixedContent}}" wx:key="key">
            <view class="item_num">{{item.num}}</view>
            <view class="item_title">{{item.title}}</view>
          </view>
        </view>
        <!-- 卡片列表 -->
        <view
          class="card-item "
          wx:for="{{contentList}}"
          wx:key="key"
          id="{{item.key}}"
        >
          <view class="card-title">
            <text>{{item.title}}</text>
            <text wx:if="{{type !== 'chainMap'}}"
              >重点领域：{{contentList[index].list.length}}</text
            >
            <text wx:if="{{type == 'chainMap'}}"
              >产业链：{{contentList[index].list.length}}</text
            >
          </view>
          <view class="card-content">
            <view
              class="content_item {{!listItem.purchased && 'lock'}}"
              wx:for="{{item.list}}"
              wx:key="id"
              wx:for-item="listItem"
              wx:for-index="idx"
              bindtap="onCardClick"
              data-item="{{listItem}}"
              data-index="{{idx}}"
            >
              <!-- 锁 -->
              <image
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_cyl_bg2.png"
                class="lock-img"
                wx:if="{{type == 'chainMap'&&!listItem.purchased}}"
              ></image>
              <view
                class="item_tit"
                style="width: {{listItem.purchased ? '100%' : '90%'}}"
              >
                {{listItem.title}}
              </view>
              <view>
                <view wx:if="{{type == 'chainMap'}}" class="item-count"
                  >环节</view
                >
                <view
                  wx:if="{{type == 'chainMap'}}"
                  class="item-count"
                  style="margin-bottom: 8rpx;"
                  ><text
                    style="color:#525665;"
                    >{{listItem.chain_node_count}}</text
                  >
                  个</view
                >
                <view class="item-count" style="color: #9B9EAC;">企业</view>

                <view class="item-count"
                  ><text style="color:#525665;">{{listItem.count}}</text> 家
                  <text
                    class="item-add"
                    wx:if="{{listItem.add>0}}"
                    >{{listItem.add}}</text
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
        <view
          style="height: {{type == 'chainMap'? '150rpx' :'100rpx'}};"
        ></view>
      </view>
    </scroll-view>
    <!-- 固定 -->
    <view class="bottom_txt" wx:if="{{type == 'chainMap'}}">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_zhuanshi1.png"
        class="img"
      ></image>
      <text>产业链图谱为高阶功能，按条购买后解锁使用</text>
    </view>
  </view>
</view>
