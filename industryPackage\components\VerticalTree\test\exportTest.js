// 导出功能测试文件
// 用于测试内存优化后的导出功能

const testExportFunctionality = {
  // 模拟不同设备的测试数据
  testDevices: [
    {
      name: 'iPhone 15 Pro',
      platform: 'ios',
      model: 'iPhone 15 Pro',
      pixelRatio: 3,
      expectedMemory: 6
    },
    {
      name: 'iPhone 12',
      platform: 'ios', 
      model: 'iPhone 12',
      pixelRatio: 3,
      expectedMemory: 4
    },
    {
      name: 'iPhone 8',
      platform: 'ios',
      model: 'iPhone 8',
      pixelRatio: 2,
      expectedMemory: 2
    },
    {
      name: 'Android 低端设备',
      platform: 'android',
      model: 'Android Device',
      pixelRatio: 2,
      expectedMemory: 2
    },
    {
      name: 'Android 高端设备',
      platform: 'android',
      model: 'Samsung Galaxy S23',
      pixelRatio: 3,
      expectedMemory: 2 // 保守估算
    }
  ],

  // 测试不同尺寸的Canvas
  testCanvasSizes: [
    {name: '小尺寸', width: 800, height: 600},
    {name: '中等尺寸', width: 1920, height: 1080},
    {name: '大尺寸', width: 3840, height: 2160},
    {name: '超大尺寸', width: 7680, height: 4320},
    {name: '极限尺寸', width: 10000, height: 8000}
  ],

  // 运行测试
  runTests() {
    console.log('=== 开始导出功能测试 ===');
    
    this.testDevices.forEach(device => {
      console.log(`\n--- 测试设备: ${device.name} ---`);
      this.testDeviceMemoryEstimation(device);
      
      this.testCanvasSizes.forEach(size => {
        this.testCanvasSizeValidation(device, size);
      });
    });
    
    console.log('\n=== 测试完成 ===');
  },

  // 测试设备内存估算
  testDeviceMemoryEstimation(device) {
    // 这里应该调用实际的内存估算方法
    // const estimatedMemory = this._estimateDeviceMemory(device);
    console.log(`设备: ${device.name}, 预期内存: ${device.expectedMemory}GB`);
  },

  // 测试Canvas尺寸验证
  testCanvasSizeValidation(device, size) {
    const area = size.width * size.height;
    const areaInMegapixels = (area / (1024 * 1024)).toFixed(2);
    
    // 根据设备内存计算最大安全面积
    let maxSafeArea;
    if (device.expectedMemory >= 6) {
      maxSafeArea = 16 * 1024 * 1024;
    } else if (device.expectedMemory >= 4) {
      maxSafeArea = 8 * 1024 * 1024;
    } else if (device.expectedMemory >= 3) {
      maxSafeArea = 4 * 1024 * 1024;
    } else {
      maxSafeArea = 2 * 1024 * 1024;
    }
    
    // 考虑像素比影响
    const pixelRatioFactor = Math.max(1, device.pixelRatio / 2);
    const adjustedMaxArea = Math.floor(maxSafeArea / pixelRatioFactor);
    
    const isSafe = area <= adjustedMaxArea;
    const status = isSafe ? '✅ 安全' : '❌ 需要缩放';
    
    console.log(`  ${size.name} (${size.width}x${size.height}, ${areaInMegapixels}MP): ${status}`);
    
    if (!isSafe) {
      const scaleFactor = Math.sqrt(adjustedMaxArea / area);
      const newWidth = Math.floor(size.width * scaleFactor);
      const newHeight = Math.floor(size.height * scaleFactor);
      console.log(`    建议缩放到: ${newWidth}x${newHeight} (缩放因子: ${scaleFactor.toFixed(3)})`);
    }
  },

  // 模拟内存错误测试
  testMemoryErrorHandling() {
    console.log('\n=== 内存错误处理测试 ===');
    
    const memoryErrors = [
      'buffer parameter fail. native buffer exceed size',
      'out of memory',
      'memory overflow',
      'canvas creation failed'
    ];
    
    memoryErrors.forEach(errorMsg => {
      const isMemoryError = this.isMemoryError({message: errorMsg});
      console.log(`错误信息: "${errorMsg}" -> ${isMemoryError ? '✅ 识别为内存错误' : '❌ 未识别'}`);
    });
  },

  // 检查是否是内存相关错误（复制自实际代码）
  isMemoryError(error) {
    const errorMsg = (error.errMsg || error.message || '').toLowerCase();
    return (
      errorMsg.includes('buffer') ||
      errorMsg.includes('memory') ||
      errorMsg.includes('exceed') ||
      errorMsg.includes('overflow') ||
      errorMsg.includes('out of memory')
    );
  }
};

// 导出测试对象
module.exports = testExportFunctionality;

// 如果直接运行此文件，执行测试
if (typeof wx === 'undefined') {
  // 在Node.js环境中运行测试
  testExportFunctionality.runTests();
  testExportFunctionality.testMemoryErrorHandling();
}
