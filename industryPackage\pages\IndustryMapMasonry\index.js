import {formatLargeNumber} from '../../../utils/util';
import {
  singleChainNodeNumTreeApi,
  singleChainStatisticApi
} from '../../../service/industryApi';
import Toast from '@vant/weapp/toast/toast.js';
const app = getApp();
Page({
  data: {
    statsData: [
      {
        value: '0',
        unit: '家',
        label: '企业总量'
      },
      {
        value: '0',
        unit: '个',
        label: '环节数'
      },
      {
        value: '0',
        unit: '家',
        label: '上市企业'
      },
      {
        value: '0',
        unit: '个',
        label: '专利数'
      }
    ],
    tabs: [
      {
        title: '企业图谱',
        name: 'qytp'
      },
      {
        title: '技术图谱',
        name: 'jstp'
      }
    ],
    currentTab: 'qytp', // 当前选中的tab
    canvasHeight: 800, // canvas高度，默认800rpx
    isExpanded: false, // 是否已展开全部节点
    bottomTabs: [
      {
        label: '全部',
        value: '1',
        active: true
      },
      {
        label: '上市公司',
        value: '2'
      },
      {
        label: '科技型企业',
        value: '3'
      },
      {
        label: '专精特新企业',
        value: '4'
      },
      {
        label: '高新技术企业',
        value: '5'
      }
    ],
    bottomTabs1: [
      {
        label: '全部专利',
        value: '6',
        active: true
      },
      {
        label: '发明型专利',
        value: '7'
      }
    ],
    purchased: true, // 是否购买
    masonryVipVisible: false, //是否购买弹窗
    // 企业图谱数据
    qytpTreeData: [],
    // 技术图谱数据（专利数据）
    jstpTreeData: [],
    // 当前显示的筛选标签
    currentBottomTabs: [],
    showPop: false,
    popupFixedTitle: '技术研发',
    externalFilterState: {} // 弹窗回显相关
  },
  onLoad(options) {
    let {purchased, type} = options;
    purchased = purchased == 'false' ? false : true;

    this.setData(
      {
        chain_code: options.chain_code,
        chain_name: decodeURIComponent(options.chain_name),
        purchased,
        chain_type: type === 'hot' ? 2 : type === 'classic' ? 3 : 1,
        region_type: 0 // 默认全国
      },
      () => {
        // 页面加载时初始化筛选标签
        this.initBottomTabs();
        this.getStatis();
      }
    );
  },

  onReady() {
    // 页面初次渲染完成，计算canvas高度
    this.calculateCanvasHeight();
  },

  onShow() {
    // 页面显示时重新计算高度
    this.calculateCanvasHeight();
  },
  // 获取顶部数量
  async getStatis() {
    const {chain_code, chain_type, region_type} = this.data;
    const statisticData = await singleChainStatisticApi({
      chain_code,
      chain_type,
      region_type
    });
    this.setData({
      statsData: this._formatStatsData(statisticData)
    });
  },
  // Tab点击事件
  async onTabClick(e) {
    const {name} = e.currentTarget.dataset;
    const bl = this.onNotPurchased();
    if (!bl) return;
    this.setData({
      currentTab: name,
      isExpanded: false // 切换tab时重置展开状态
    });

    // 切换筛选标签数据
    await this.switchBottomTabs(name);

    console.log('切换到tab:', name);
  },

  // 获取当前tab对应的数据
  getCurrentTreeData() {
    return this.data.currentTab === 'qytp'
      ? this.data.qytpTreeData
      : this.data.jstpTreeData;
  },

  // 计算canvas高度：100vh - 360rpx
  calculateCanvasHeight() {
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight; // 屏幕高度(px)
      const windowWidth = systemInfo.windowWidth; // 屏幕宽度(px)

      // 计算rpx到px的转换比例
      const rpxToPxRatio = windowWidth / 750;

      // 头部高度360rpx转换为px
      const headHeightPx = 360 * rpxToPxRatio;

      // 计算剩余高度(px)
      const remainingHeightPx = windowHeight - headHeightPx;

      // 转换回rpx
      const canvasHeightRpx = Math.floor(remainingHeightPx / rpxToPxRatio);

      // 确保高度不会太小
      const finalHeight = Math.max(400, canvasHeightRpx);
      this.setData({
        canvasHeight: finalHeight
      });
    } catch (error) {
      console.error('计算canvas高度失败:', error);
      // 使用默认高度
      this.setData({
        canvasHeight: 1200
      });
    }
  },

  // 页面尺寸变化时重新计算
  onResize() {
    this.calculateCanvasHeight();
  },

  // 全部展开/收起按钮点击事件
  onExpandAllClick() {
    const currentExpanded = this.data.isExpanded;
    const currentTab = this.data.currentTab;
    const bl = this.onNotPurchased();
    if (!bl) return;
    // 根据当前tab获取对应的组件ID
    const componentId = currentTab === 'qytp' ? 'qytpTree' : 'jstpTree';

    // 直接通过selectComponent获取组件实例
    const treeComponent = this.selectComponent(`#${componentId}`);

    if (treeComponent) {
      if (currentExpanded) {
        // 当前是展开状态，执行收起操作
        if (typeof treeComponent.collapseAll === 'function') {
          treeComponent.collapseAll();
        } else {
          console.warn('VerticalTree组件不支持collapseAll方法');
        }
      } else {
        // 当前是收起状态，执行展开操作
        if (typeof treeComponent.expandAll === 'function') {
          treeComponent.expandAll();
        } else {
          console.warn('VerticalTree组件不支持expandAll方法');
        }
      }

      // 切换状态
      this.setData({
        isExpanded: !currentExpanded
      });
    } else {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 下载图谱按钮点击事件
  onDownloadClick() {
    const bl = this.onNotPurchased();
    if (!bl) return;
    const currentTab = this.data.currentTab;

    // 根据当前tab获取对应的组件ID
    const componentId = currentTab === 'qytp' ? 'qytpTree' : 'jstpTree';

    // 直接通过selectComponent获取组件实例
    const treeComponent = this.selectComponent(`#${componentId}`);

    if (treeComponent) {
      // 检查组件是否有导出图片的方法
      if (typeof treeComponent.handleExportImage === 'function') {
        wx.showLoading({
          title: '正在生成图片...',
          mask: true
        });

        try {
          treeComponent.handleExportImage();
        } catch (error) {
          console.error('导出图片失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '导出失败',
            icon: 'none'
          });
        }
      } else {
        console.warn('VerticalTree组件不支持导出图片功能');
        wx.showToast({
          title: '暂不支持导出',
          icon: 'none'
        });
      }
    } else {
      console.warn(`未找到VerticalTree组件: ${componentId}`);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 筛选项点击事件
  async onFilterItemClick(e) {
    const {index} = e.currentTarget.dataset;
    const currentTab = this.data.currentTab;
    const bl = this.onNotPurchased();
    if (!bl) return;
    // 根据当前tab获取对应的筛选数据
    const tabsKey = currentTab === 'qytp' ? 'bottomTabs' : 'bottomTabs1';
    const currentTabs = [...this.data[tabsKey]];

    // 更新选中状态
    currentTabs.forEach((item, i) => {
      item.active = i === index;
    });

    // 更新对应的筛选数据
    this.setData({
      [tabsKey]: currentTabs,
      currentBottomTabs: currentTabs,
      isExpanded: false
    });

    const selectedFilter = currentTabs[index];
    console.log('选中筛选项:', selectedFilter);

    // 根据筛选项重新加载数据
    await this.filterAndRenderTree(selectedFilter.value);
  },

  // 初始化筛选标签
  async initBottomTabs() {
    const currentTab = this.data.currentTab;
    const tabsData =
      currentTab === 'qytp' ? this.data.bottomTabs : this.data.bottomTabs1;

    this.setData(
      {
        currentBottomTabs: tabsData
      },
      () => {
        // 初始化后获取图谱数据
        this.loadTreeData();
      }
    );
  },

  // 获取图谱数据
  async loadTreeData(countType = null) {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      const {chain_code, currentTab} = this.data;

      // 检查必要参数
      if (!chain_code) {
        console.error('chain_code 参数缺失');
        return;
      }

      // 确定当前选中的筛选类型
      let selectedCountType = countType;
      if (!selectedCountType) {
        const currentTabs =
          currentTab === 'qytp' ? this.data.bottomTabs : this.data.bottomTabs1;
        const activeTab = currentTabs.find(tab => tab.active);
        selectedCountType = activeTab ? activeTab.value : '1';
      }

      // 调用 itypeApi 获取数据
      const params = {
        chain_code: chain_code,
        count_type: parseInt(selectedCountType)
      };

      // console.log('请求参数:', params);
      const treeData = await singleChainNodeNumTreeApi(params);

      if (treeData?.childs?.length) {
        treeData.childs = treeData.childs.sort(
          (a, b) => a.link_type - b.link_type
        );
      }
      // 转换数据格式
      const formattedData = this.formatTreeData([treeData]);

      // 根据当前标签页更新对应的数据
      if (currentTab === 'qytp') {
        this.setData({
          qytpTreeData: formattedData
        });
      } else {
        this.setData({
          jstpTreeData: formattedData
        });
      }
    } catch (error) {
      console.error('获取图谱数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 格式化树形数据，将 API 返回的数据转换为组件需要的格式
  formatTreeData(apiData) {
    // console.log('原始API数据:', apiData);

    // 处理单个对象的情况
    let dataArray = apiData;
    if (!Array.isArray(apiData)) {
      if (apiData && typeof apiData === 'object') {
        dataArray = [apiData];
      } else {
        // console.warn('API返回数据格式异常:', apiData);
        return [];
      }
    }

    const formatNode = node => {
      // 过滤掉无效节点
      if (!node || typeof node !== 'object' || !node.name) {
        return null;
      }
      const formattedNode = {
        name: node.name,
        count: Math.abs(node.count || 0), // 取绝对值，避免负数显示
        expertchain_code: node.expertchain_code,
        link_type: node.link_type,
        parent: node.parent,
        level: node.level
      };

      // 处理子节点
      if (node.childs && Array.isArray(node.childs) && node.childs.length > 0) {
        const validChildren = node.childs
          .map(child => formatNode(child))
          .filter(child => child !== null);

        if (validChildren.length > 0) {
          formattedNode.children = validChildren;
        }
      }

      return formattedNode;
    };

    const result = dataArray
      .map(node => formatNode(node))
      .filter(node => node !== null);

    // console.log('格式化后的数据:', result);
    return result;
  },

  // 切换筛选标签数据
  async switchBottomTabs(tabName) {
    const tabsData =
      tabName === 'qytp' ? this.data.bottomTabs : this.data.bottomTabs1;

    // 重置选中状态为第一项
    const resetTabs = tabsData.map((item, index) => ({
      ...item,
      active: index === 0
    }));

    // 更新对应的数据源
    const tabsKey = tabName === 'qytp' ? 'bottomTabs' : 'bottomTabs1';
    this.setData({
      [tabsKey]: resetTabs,
      currentBottomTabs: resetTabs
    });

    // 重新加载数据（默认选中第一项的值）
    const firstTabValue = resetTabs[0]?.value || '1';
    await this.loadTreeData(firstTabValue);
  },

  // 根据筛选条件过滤数据并重新渲染图谱
  async filterAndRenderTree(filterValue) {
    // 直接调用 loadTreeData 方法，传入筛选值
    await this.loadTreeData(filterValue);
  },
  // 获取原始企业图谱数据
  getOriginalQytpData() {
    // 返回完整的原始数据，这里应该是从服务器获取或者缓存的完整数据
    return this.data.qytpTreeData;
  },

  // 获取原始技术图谱数据
  getOriginalJstpData() {
    // 返回完整的原始数据
    return this.data.jstpTreeData;
  },
  // 点击图谱---打开企业列表
  onNodeClick(e) {
    const {name, expertchain_code, depth} = e.detail.node;
    const {currentBottomTabs} = this.data;
    const label = currentBottomTabs.find(tab => tab.active)?.label;
    const bl = this.onNotPurchased();
    if (!bl) return;
    if (depth === 1) return;
    const tempExternalFilterState = {
      industrial_list: {
        code: expertchain_code,
        name: name
      }
    };
    if (label === '上市公司') {
      tempExternalFilterState['filterParams'] = {
        listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
      };
    } else if (label === '科技型企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN', 'MST', 'G', 'U', 'GT']
      };
    } else if (label === '专精特新企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['SME', 'CMI', 'SG', 'IME']
      };
    } else if (label === '高新技术企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN']
      };
    } else if (label === '全部专利') {
      tempExternalFilterState['filterParams'] = {
        patent_info: ['1', '2', '3', '4']
      };
    } else if (label === '发明型专利') {
      tempExternalFilterState['filterParams'] = {
        patent_info: ['4', '1']
      };
    }
    // 这里加一个参数 用于跳转地图模式带进去  原始code
    tempExternalFilterState['originalCode'] = this.data.chain_code;
    tempExternalFilterState['originalName'] = this.data.chain_name;
    this.setData({
      showPop: true,
      popupFixedTitle: name,
      externalFilterState: tempExternalFilterState
    });
  },
  // 处理弹窗内部滑动，阻止冒泡
  onPopupTouchMove() {
    // 阻止事件冒泡，允许弹窗内部滚动而不影响外部
    return false;
  },
  onClose() {
    this.setData({
      showPop: false
    });
  },

  // 获取当前活动的树形组件
  getCurrentTreeComponent() {
    const {currentTab} = this.data;
    if (currentTab === 'qytp') {
      return this.selectComponent('#qytpTree');
    } else if (currentTab === 'jstp') {
      return this.selectComponent('#jstpTree');
    }
    return null;
  },
  // 拦截没有购买的情况

  onNotPurchased() {
    const {purchased} = this.data;
    if (!purchased) {
      this.setData({
        masonryVipVisible: true
      });
      return false;
    }
    return true;
  },

  onVipClose() {
    this.setData({
      masonryVipVisible: false
    });
  },
  onSend() {
    this.onVipClose();
    // app.showToast(
    //   '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢'
    // );
    Toast({
      message: '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢',
      duration: 3000,
      selector: '#van-toast'
    });
  },
  /**
   * 格式化统计数据
   * @param {Object} data - 统计数据
   * @returns {Array} 格式化后的统计数据
   */
  _formatStatsData(data) {
    const {statsData} = this.data;
    const formattedStats = [...statsData];

    formattedStats[0].value = formatLargeNumber(data.ent_cnt || 0);
    formattedStats[1].value = formatLargeNumber(data.chain_cnt || 0);
    formattedStats[2].value = formatLargeNumber(data.listed_ent_cnt || 0);
    formattedStats[3].value = formatLargeNumber(data.patent_cnt || 0);

    return formattedStats;
  }
});
