import {setTagColor} from '../../utils/util.js';
import constant from '../../utils/constant';
import {hijack} from '../../utils/route';
const app = getApp();

Component({
  externalClasses: ['hcard'], // ps外面使用  hcard="custom-card-style" 外面是这么使用的
  options: {
    addGlobalClass: true,
    styleIsolation: 'apply-shared'
  },
  properties: {
    obj: {
      //传进来的数组 ，渲染 -目前是写死的 --通过setTagColor处理一下标签
      type: Object,
      observer(val) {
        val['register_date'] =
          val.register_date?.indexOf('NaN') >= 0 ? '--' : val.register_date;
        if (val && val.tags.length > 0) {
          this.setData({
            tags: setTagColor(val.tags), //['世界500强', '续存', '纳税A级企业', '微型', '央企']
            objData: val
          });
        }
      }
    },
    btn: {
      //是否是新增客商添加按鈕
      type: String,
      value: 'collect' //添加，监控，收藏
    },
    isBangDan: {
      //是否是榜单
      type: Boolean,
      value: false
    }
  },
  data: {
    tags: [],
    objData: {}
  },
  methods: {
    // 收藏
    collect: hijack(
      function (e) {
        const item =
          e.target.dataset['item'] || e.currentTarget.dataset['item'];
        const index =
          e.target.dataset['index'] || e.currentTarget.dataset['index'];
        console.log('点击收藏', index);
        this.triggerEvent('cardFun', {
          type: 'collect',
          index,
          data: item
        });
      },
      {
        type: 'searchs-btn',
        app: app
      }
    ),
    // 联系方式
    relation: hijack(
      function (e) {
        const item =
          e.target.dataset['item'] || e.currentTarget.dataset['item'];
        this.triggerEvent('cardFun', {
          type: 'relation',
          data: item
        });
      },
      {
        type: 'searchs-btn',
        app: app
      }
    ),
    // 官网
    official(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      if (item?.official_website || item.website) {
        wx.setClipboardData({
          data: item?.official_website || item.website,
          success(res) {
            wx.showToast({
              title: '复制成功',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '该企业暂无官网',
          icon: 'none'
        });
      }
    },
    // 发地址
    site(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      console.log(item);
      this.triggerEvent('cardFun', {
        type: 'site',
        data: item
      });
    },
    // 添加
    add(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
      this.triggerEvent('cardFun', {
        type: 'add',
        data: item
      });
    },
    goDetail: hijack(
      function (e) {
        let {ent_id, ent_name, logo} = e.currentTarget.dataset.item;
        if (typeof ent_name === 'object') {
          ent_name = ent_name.join('');
        }
        if (ent_id) {
          this.triggerEvent('handleTit', {
            ent_id,
            ent_name,
            logo
          });
          const url = encodeURIComponent(
            `https://reporth5.handidit.com?entId=${ent_id}`
          );
          app.route(this, `/subPackage/pages/webs/index?url=${url}`);
        }
      },
      {
        type: 'searchs-btn',
        app: app
      }
    ),
    errorFunction(e) {
      this.setData({
        'objData.src':
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png'
      });
    }
  }
});
