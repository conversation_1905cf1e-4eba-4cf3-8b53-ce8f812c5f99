// 导出图片功能混入逻辑

// 导入常量配置
const {
  CANVAS_CONSTANTS,
  UTILS_CONSTANTS,
  EXPORT_CONSTANTS,
  ERROR_MESSAGES
} = require('../config/constants.js');

const exportMixin = {
  data: {
    // 导出相关状态
    _isExporting: false,
    _exportCanvas: null,
    _exportCtx: null,
    // 权限相关状态
    openSet: false
  },

  methods: {
    // 导出按钮点击处理
    handleExportImage() {
      if (this.data._isExporting) return;

      wx.showLoading({title: '正在生成图片...'});
      this.setData({_isExporting: true});

      // 使用wx.nextTick确保UI更新后再执行
      wx.nextTick(() => {
        this.exportImage();
      });
    },

    // 核心导出逻辑
    exportImage() {
      try {
        // 1. 计算完整内容尺寸
        const fullSize = this._calculateFullContentSize();

        // 2. 检查内容尺寸是否过大
        if (!this._validateContentSize(fullSize)) {
          this._handleExportError('图片内容过大，请尝试缩小显示范围');
          return;
        }

        // 3. 创建导出用临时canvas
        const {canvas, ctx} = this._createExportCanvas(
          fullSize.width,
          fullSize.height
        );

        // 4. 在临时canvas上渲染完整内容
        this._renderFullContentToCanvas(ctx, fullSize);

        // 5. 导出图片
        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'png',
          quality: 1,
          success: res => {
            this._handleExportSuccess(res.tempFilePath);
          },
          fail: err => {
            // 检查是否是内存相关错误
            if (this._isMemoryError(err)) {
              this._handleMemoryError(err);
            } else {
              this._handleExportError('导出图片失败', err);
            }
          }
        });
      } catch (error) {
        // 检查是否是内存相关错误
        if (this._isMemoryError(error)) {
          this._handleMemoryError(error);
        } else {
          this._handleExportError('生成图片失败', error);
        }
      }
    },

    // 验证内容尺寸
    _validateContentSize(fullSize) {
      const maxArea = EXPORT_CONSTANTS.MAX_SAFE_AREA;
      const currentArea = fullSize.width * fullSize.height;

      if (currentArea > maxArea) {
        console.warn(
          `内容尺寸过大: ${fullSize.width}x${fullSize.height} (${currentArea}像素), 超过限制: ${maxArea}像素`
        );
        return false;
      }

      return true;
    },

    // 检查是否是内存相关错误
    _isMemoryError(error) {
      const errorMsg = (error.errMsg || error.message || '').toLowerCase();
      return (
        errorMsg.includes('buffer') ||
        errorMsg.includes('memory') ||
        errorMsg.includes('exceed') ||
        errorMsg.includes('overflow') ||
        errorMsg.includes('out of memory')
      );
    },

    // 处理内存错误
    _handleMemoryError(error) {
      wx.hideLoading();
      this.setData({_isExporting: false});
      this._cleanupExportResources();

      console.error('内存不足导致导出失败:', error);

      wx.showModal({
        title: '导出失败',
        content:
          '设备内存不足，无法导出完整图片。建议：\n1. 关闭其他应用释放内存\n2. 缩小显示内容后重试\n3. 使用截屏功能保存',
        showCancel: true,
        cancelText: '知道了',
        confirmText: '重试',
        success: res => {
          if (res.confirm) {
            // 用户选择重试，延迟一下再执行
            setTimeout(() => {
              this.handleExportImage();
            }, 1000);
          }
        }
      });
    },

    // 计算完整内容尺寸
    _calculateFullContentSize() {
      let bounds = {
        minX: Infinity,
        maxX: -Infinity,
        minY: Infinity,
        maxY: -Infinity
      };

      // 获取当前布局数据
      const layout = this.currentLayout || this._getCurrentLayout();

      if (layout && layout.nodes && layout.nodes.length > 0) {
        // 遍历所有节点
        layout.nodes.forEach(node => {
          const nodeBounds = this._getNodeBounds(node);
          bounds.minX = Math.min(bounds.minX, nodeBounds.left);
          bounds.maxX = Math.max(bounds.maxX, nodeBounds.right);
          bounds.minY = Math.min(bounds.minY, nodeBounds.top);
          bounds.maxY = Math.max(bounds.maxY, nodeBounds.bottom);
        });
      } else {
        // 如果没有布局数据，使用默认尺寸
        console.warn('没有找到布局数据，使用默认尺寸');
        bounds = {
          minX: 0,
          maxX: this.rpxToPx(750),
          minY: 0,
          maxY: this.rpxToPx(1200)
        };
      }

      // 考虑连接线的边界
      if (layout && layout.connections && layout.connections.length > 0) {
        const lineBounds = this._getConnectionLineBounds(layout.connections);
        bounds.minX = Math.min(bounds.minX, lineBounds.minX);
        bounds.maxX = Math.max(bounds.maxX, lineBounds.maxX);
        bounds.minY = Math.min(bounds.minY, lineBounds.minY);
        bounds.maxY = Math.max(bounds.maxY, lineBounds.maxY);
      }

      // 添加边距
      const padding = this.rpxToPx(EXPORT_CONSTANTS.PADDING);

      return {
        width: bounds.maxX - bounds.minX + padding * 2,
        height: bounds.maxY - bounds.minY + padding * 2,
        offsetX: -bounds.minX + padding,
        offsetY: -bounds.minY + padding,
        contentBounds: bounds
      };
    },

    // 获取当前布局数据
    _getCurrentLayout() {
      try {
        // 如果没有缓存的布局，重新计算
        const treeData = this._prepareTreeData();
        if (treeData && treeData.length > 0) {
          return this.calculateTreeLayout(treeData);
        }
      } catch (error) {
        console.error('获取当前布局失败:', error);
      }
      return null;
    },

    // 获取单个节点边界
    _getNodeBounds(node) {
      return {
        left: node.x,
        right: node.x + node.width,
        top: node.y - node.height / 2,
        bottom: node.y + node.height / 2
      };
    },

    // 获取连接线边界
    _getConnectionLineBounds(connections) {
      if (!connections || connections.length === 0) {
        return {
          minX: 0,
          maxX: 0,
          minY: 0,
          maxY: 0
        };
      }

      let minX = Infinity,
        maxX = -Infinity;
      let minY = Infinity,
        maxY = -Infinity;

      connections.forEach(conn => {
        // 连接线的起点和终点
        minX = Math.min(minX, conn.fromX, conn.toX);
        maxX = Math.max(maxX, conn.fromX, conn.toX);
        minY = Math.min(minY, conn.fromY, conn.toY);
        maxY = Math.max(maxY, conn.fromY, conn.toY);
      });

      return {minX, maxX, minY, maxY};
    },

    // 创建导出用临时canvas
    _createExportCanvas(width, height) {
      try {
        // 检查并限制Canvas尺寸，防止内存溢出
        const {adjustedWidth, adjustedHeight, scaleFactor} =
          this._validateAndAdjustCanvasSize(width, height);

        console.log(
          `原始尺寸: ${width}x${height}, 调整后: ${adjustedWidth}x${adjustedHeight}, 缩放因子: ${scaleFactor}`
        );

        // 使用离屏canvas
        const canvas = wx.createOffscreenCanvas({
          type: '2d',
          width: adjustedWidth,
          height: adjustedHeight
        });

        const ctx = canvas.getContext('2d');

        // 获取设备像素比，但要考虑内存限制
        const dpr = this._getSafeDevicePixelRatio(
          adjustedWidth,
          adjustedHeight
        );

        // 设置高清显示（使用安全的像素比）
        canvas.width = adjustedWidth * dpr;
        canvas.height = adjustedHeight * dpr;
        ctx.scale(dpr, dpr);

        // 如果进行了尺寸调整，需要相应缩放绘制内容
        if (scaleFactor < 1) {
          ctx.scale(scaleFactor, scaleFactor);
        }

        // 设置默认样式
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 保存canvas引用和缩放信息用于清理
        this.setData({
          _exportCanvas: canvas,
          _exportCtx: ctx,
          _exportScaleFactor: scaleFactor
        });

        return {canvas, ctx};
      } catch (error) {
        console.error('创建导出canvas失败:', error);
        throw new Error('创建导出canvas失败');
      }
    },

    // 验证并调整Canvas尺寸，防止内存溢出
    _validateAndAdjustCanvasSize(width, height) {
      // 获取设备信息
      const systemInfo = this._getSystemInfo();
      const deviceMemory = systemInfo.deviceMemory || 2; // GB，默认2GB
      const pixelRatio = systemInfo.pixelRatio || 2;

      // 计算最大安全尺寸（基于设备内存和像素比）
      const maxSafeArea = this._calculateMaxSafeCanvasArea(
        deviceMemory,
        pixelRatio
      );
      const currentArea = width * height;

      console.log(
        `设备内存: ${deviceMemory}GB, 像素比: ${pixelRatio}, 最大安全面积: ${maxSafeArea}, 当前面积: ${currentArea}`
      );

      // 如果当前尺寸在安全范围内，直接返回
      if (currentArea <= maxSafeArea) {
        return {
          adjustedWidth: width,
          adjustedHeight: height,
          scaleFactor: 1
        };
      }

      // 计算缩放因子
      const scaleFactor = Math.sqrt(maxSafeArea / currentArea);
      const adjustedWidth = Math.floor(width * scaleFactor);
      const adjustedHeight = Math.floor(height * scaleFactor);

      // 确保最小尺寸
      const finalWidth = Math.max(adjustedWidth, EXPORT_CONSTANTS.MIN_WIDTH);
      const finalHeight = Math.max(adjustedHeight, EXPORT_CONSTANTS.MIN_HEIGHT);

      return {
        adjustedWidth: finalWidth,
        adjustedHeight: finalHeight,
        scaleFactor: Math.min(
          scaleFactor,
          finalWidth / width,
          finalHeight / height
        )
      };
    },

    // 计算最大安全Canvas面积
    _calculateMaxSafeCanvasArea(deviceMemory, pixelRatio) {
      // 基础安全面积（像素）
      let baseMaxArea;

      if (deviceMemory >= EXPORT_CONSTANTS.MEMORY_THRESHOLDS.HIGH_END) {
        baseMaxArea = EXPORT_CONSTANTS.SAFE_AREAS.HIGH_END;
      } else if (deviceMemory >= EXPORT_CONSTANTS.MEMORY_THRESHOLDS.MID_HIGH) {
        baseMaxArea = EXPORT_CONSTANTS.SAFE_AREAS.MID_HIGH;
      } else if (deviceMemory >= EXPORT_CONSTANTS.MEMORY_THRESHOLDS.MID_RANGE) {
        baseMaxArea = EXPORT_CONSTANTS.SAFE_AREAS.MID_RANGE;
      } else {
        baseMaxArea = EXPORT_CONSTANTS.SAFE_AREAS.LOW_END;
      }

      // 根据像素比调整（高像素比设备实际内存消耗更大）
      const pixelRatioFactor = Math.max(1, pixelRatio / 2);
      return Math.floor(baseMaxArea / pixelRatioFactor);
    },

    // 获取安全的设备像素比
    _getSafeDevicePixelRatio(width, height) {
      const originalDpr = this._getDevicePixelRatio();
      const canvasArea = width * height;

      // 对于大尺寸Canvas，降低像素比以节省内存
      if (canvasArea > 2 * 1024 * 1024) {
        // 2M像素以上
        return Math.min(originalDpr, 1.5);
      } else if (canvasArea > 1 * 1024 * 1024) {
        // 1M像素以上
        return Math.min(originalDpr, 2);
      }

      return originalDpr;
    },

    // 获取系统信息
    _getSystemInfo() {
      try {
        if (wx.getDeviceInfo && wx.getWindowInfo) {
          const deviceInfo = wx.getDeviceInfo();
          const windowInfo = wx.getWindowInfo();

          // 尝试从设备型号推断内存大小
          const deviceMemory = this._estimateDeviceMemory(deviceInfo);

          return {
            deviceMemory,
            pixelRatio: windowInfo.pixelRatio || deviceInfo.pixelRatio || 2,
            platform: deviceInfo.platform,
            model: deviceInfo.model
          };
        } else {
          // 兼容旧版本API
          const systemInfo = wx.getSystemInfoSync();
          return {
            deviceMemory: this._estimateDeviceMemory(systemInfo),
            pixelRatio: systemInfo.pixelRatio || 2,
            platform: systemInfo.platform,
            model: systemInfo.model
          };
        }
      } catch (error) {
        console.warn('获取系统信息失败，使用默认值:', error);
        return {
          deviceMemory: 2, // 默认2GB
          pixelRatio: 2,
          platform: 'unknown',
          model: 'unknown'
        };
      }
    },

    // 根据设备型号估算内存大小
    _estimateDeviceMemory(deviceInfo) {
      const model = (deviceInfo.model || '').toLowerCase();
      const platform = deviceInfo.platform || '';

      // iOS设备内存估算
      if (platform === 'ios') {
        if (model.includes('iphone 15') || model.includes('iphone 14')) {
          return 6; // iPhone 14/15系列通常6GB+
        } else if (model.includes('iphone 13') || model.includes('iphone 12')) {
          return 4; // iPhone 12/13系列通常4-6GB
        } else if (model.includes('iphone 11') || model.includes('iphone x')) {
          return 3; // iPhone X/11系列通常3-4GB
        } else {
          return 2; // 较老的iPhone
        }
      }

      // Android设备内存估算（更保守）
      if (platform === 'android') {
        // 对于Android设备，由于碎片化严重，采用更保守的估算
        return 2; // 默认按2GB处理，避免内存问题
      }

      // 其他平台默认值
      return 2;
    },

    // 获取设备像素比
    _getDevicePixelRatio() {
      try {
        if (wx.getWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          return windowInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        } else if (wx.getDeviceInfo) {
          const deviceInfo = wx.getDeviceInfo();
          return deviceInfo.pixelRatio || UTILS_CONSTANTS.DEFAULT_DPR;
        } else {
          return UTILS_CONSTANTS.DEFAULT_DPR;
        }
      } catch (error) {
        console.warn('获取设备像素比失败，使用默认值:', error);
        return UTILS_CONSTANTS.DEFAULT_DPR;
      }
    },

    // 在临时canvas上渲染完整内容
    _renderFullContentToCanvas(exportCtx, fullSize) {
      // 保存当前渲染状态
      const originalState = this._saveCurrentRenderState();

      try {
        // 调整渲染参数用于导出
        this._adjustRenderingForExport(exportCtx, fullSize);

        // 获取布局数据
        const layout = this.currentLayout || this._getCurrentLayout();
        if (!layout) {
          throw new Error('无法获取布局数据');
        }

        // 直接渲染到导出canvas，不使用拖拽偏移
        this._renderTreeContentForExport(layout, exportCtx, fullSize);
      } finally {
        // 恢复原始渲染状态
        this._restoreRenderingState(originalState);
      }
    },

    // 专门用于导出的渲染方法
    _renderTreeContentForExport(layout, exportCtx, fullSize) {
      // 保存导出canvas状态
      exportCtx.save();

      try {
        // 设置背景色（与正常canvas保持一致）
        this._setExportCanvasBackground(exportCtx, fullSize);

        // 应用偏移，确保内容在canvas中居中
        exportCtx.translate(fullSize.offsetX, fullSize.offsetY);

        // 临时设置ctx为导出canvas
        const originalCtx = this.ctx;
        this.ctx = exportCtx;

        // 绘制连接线
        this.drawConnections(layout);
        // 绘制节点
        this.drawNodes(layout);

        // 恢复原始ctx
        this.ctx = originalCtx;
      } finally {
        // 恢复导出canvas状态
        exportCtx.restore();
      }
    },

    // 设置导出canvas的背景色
    _setExportCanvasBackground(exportCtx, fullSize) {
      // 使用与正常canvas相同的背景色
      exportCtx.fillStyle = EXPORT_CONSTANTS.BACKGROUND_COLOR;
      exportCtx.fillRect(0, 0, fullSize.width, fullSize.height);
    },

    // 保存当前渲染状态
    _saveCurrentRenderState() {
      return {
        ctx: this.data.ctx,
        translateX: this.data.translateX,
        translateY: this.data.translateY,
        isExporting: this.data._isExporting
      };
    },

    // 调整渲染参数用于导出
    _adjustRenderingForExport(exportCtx, fullSize) {
      // 只设置导出状态，不改变translateX和translateY
      this.setData({
        _isExporting: true
      });
    },

    // 恢复原始渲染状态
    _restoreRenderingState(originalState) {
      this.setData({
        ctx: originalState.ctx,
        translateX: originalState.translateX,
        translateY: originalState.translateY,
        _isExporting: originalState.isExporting
      });
    },

    // 处理导出成功
    _handleExportSuccess(tempFilePath) {
      wx.hideLoading();
      this.setData({_isExporting: false});
      this._cleanupExportResources();

      if (tempFilePath) {
        this.saveImage(tempFilePath);
      } else {
        this._showExportSuccess();
      }
    },

    // 处理导出错误
    _handleExportError(message, error) {
      wx.hideLoading();
      this.setData({_isExporting: false});
      this._cleanupExportResources();
      console.error(message, error);
      this._showExportError(message);
    },

    // 保存到相册
    saveImage(tempFilePath) {
      const that = this;

      wx.getSetting({
        success: res => {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            // 没有权限，请求授权
            that._requestPhotoAlbumPermission(tempFilePath);
          } else {
            // 有权限，直接保存
            that._saveToPhotoAlbum(tempFilePath);
          }
        },
        fail: err => {
          that._handleExportError('获取权限信息失败', err);
        }
      });
    },

    // 请求相册权限
    _requestPhotoAlbumPermission(tempFilePath) {
      const that = this;

      wx.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          that._saveToPhotoAlbum(tempFilePath);
        },
        fail: () => {
          // 用户拒绝授权，显示设置引导
          that.setData({openSet: true});
        }
      });
    },

    // 保存到相册
    _saveToPhotoAlbum(tempFilePath) {
      const that = this;

      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: () => {
          that._showExportSuccess();
        },
        fail: err => {
          if (err.errMsg === 'saveImageToPhotosAlbum:fail auth deny') {
            that.setData({openSet: true});
          } else {
            that._showExportError('保存失败，请截屏保存', err);
          }
        }
      });
    },

    // 权限弹窗确认
    openSetSubmit() {
      this.setData({openSet: false});
      wx.openSetting({
        success: res => {
          if (res.authSetting['scope.writePhotosAlbum']) {
            wx.showToast({title: '权限已开启', icon: 'success'});
          }
        }
      });
    },

    // 权限弹窗关闭
    openSetClose() {
      this.setData({openSet: false});
    },

    // 清理导出资源
    _cleanupExportResources() {
      this.setData({
        _exportCanvas: null,
        _exportCtx: null,
        _exportScaleFactor: null
      });
    },

    // 显示导出错误
    _showExportError(message) {
      wx.showToast({
        title: message || '导出失败',
        icon: 'none',
        duration: 2000
      });
    },

    // 显示导出成功
    _showExportSuccess() {
      wx.showToast({
        title: '已保存到相册',
        icon: 'success',
        duration: 2000
      });
    }
  }
};

module.exports = {exportMixin};
