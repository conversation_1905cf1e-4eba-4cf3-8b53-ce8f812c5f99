<immersive-navbar id="navbar" title="产业链图谱" show-back-btn="{{true}}">
  <view slot="content" class="content">
    <!-- 头部 -->
    <view class="head">
      <!-- 4个固定item -->
      <view class="stats-container">
        <view class="stats-item" wx:for="{{statsData}}" wx:key="index">
          <view class="stats-value">
            {{item.value}}<text class="stats-unit">{{item.unit}}</text>
          </view>
          <view class="stats-label">{{item.label}}</view>
        </view>
      </view>
      <!-- 2个可以切换得tab -->
      <view class="tabs-container">
        <view
          class="tabs-item {{currentTab === item.name ? 'active' : ''}}"
          wx:for="{{tabs}}"
          wx:key="index"
          data-name="{{item.name}}"
          bindtap="onTabClick"
        >
          {{item.title}}
        </view>
      </view>
    </view>
    <!--图谱部分 -->
    <view class="box">
      <VerticalTree
        wx:if="{{currentTab === 'qytp'}}"
        id="qytpTree"
        tree-data="{{qytpTreeData}}"
        canvas-height="{{canvasHeight}}"
        enable-drag="{{true}}"
        enable-scale="{{true}}"
        debug-mode="{{false}}"
        enable-node-click="{{true}}"
        bind:nodeClick="onNodeClick"
        purchased="{{purchased}}"
        bind:openVip="onNotPurchased"
      />
      <VerticalTree
        wx:if="{{currentTab === 'jstp'}}"
        id="jstpTree"
        tree-data="{{jstpTreeData}}"
        canvas-height="{{canvasHeight}}"
        enable-drag="{{true}}"
        enable-scale="{{true}}"
        debug-mode="{{false}}"
        enable-node-click="{{true}}"
        bind:nodeClick="onNodeClick"
      />
      <!-- 功能按钮组 -->
      <view class="btn_group">
        <view class="btn_group_item" catchtap="onExpandAllClick">
          <image
            class="btn_icon"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_expand.png"
          ></image>
          <view class="btn_text_line1">全部</view>
          <view class="btn_text_line2">{{isExpanded ? '收起' : '展开'}}</view>
        </view>
        <view class="btn_group_item" catchtap="onDownloadClick">
          <image
            class="btn_icon"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_download.png"
          ></image>
          <view class="btn_text_line1">下载</view>
          <view class="btn_text_line2">图谱</view>
        </view>
      </view>
      <!-- 筛选条件 -->
      <scroll-view
        scroll-x="{{true}}"
        class="filter_scroll"
        enable-flex="{{true}}"
        show-scrollbar="{{false}}"
      >
        <view
          class="filter_scroll_box {{ currentTab === 'jstp' && 'filter_scroll_jstp'}}"
        >
          <view
            wx:for="{{currentBottomTabs}}"
            wx:key="index"
            class="filter_item {{item.active ? 'active' : ''}}"
            data-index="{{index}}"
            bindtap="onFilterItemClick"
          >
            {{item.label}}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 未购买VIP -->
    <MasonryVip
      visible="{{masonryVipVisible}}"
      bindclose="onVipClose"
      bindsend="onSend"
      chain_code="{{chain_code}}"
      chain_name="{{chain_name}}"
    />
  </view>
</immersive-navbar>
<van-popup
  show="{{ showPop }}"
  bind:close="onClose"
  position="right"
  custom-style="height: 100vh; width: 90vw;"
  z-index="9999"
>
  <view class="popup-content" catchtouchmove="onPopupTouchMove">
    <!-- 关闭按钮放在弹窗内部 -->
    <view class="popup-close-btn" catchtap="onClose">
      <image src="/image/close.png" />
    </view>
    <view class="zhanwei"></view>
    <view class="popup_box">
      <BusinessListComponent
        wx:if="{{showPop}}"
        dropDownMenuConfig="{{['region','filter']}}"
        fixedTitle="{{popupFixedTitle}}"
        showMapMode="{{true}}"
        popupMode="{{true}}"
        popupTopOffset="{{160}}"
        isIndustryMap="{{true}}"
        externalFilterState="{{externalFilterState}}"
        dropDownMenuTitle="{{['全国', '更多筛选']}}"
      />
    </view>
  </view>
</van-popup>
<van-toast id="van-toast" />
