// 树形组件常量配置文件
// 此文件包含了 VerticalTree 组件的所有常量配置，便于统一管理和维护

// ==================== 布局相关常量 ====================
const LAYOUT_CONSTANTS = {
  // 间距配置 - 控制节点之间的距离
  HORIZONTAL_GAP: 32, // rpx 水平线固定长度，连接父子节点的水平距离
  VERTICAL_GAP: 20, // rpx 最小垂直间距，同级节点之间的最小垂直距离

  // 树形结构层级定义 - 用于区分不同层级的节点
  TREE_LEVELS: {
    ROOT: 0, // 根节点层级
    FIRST: 1, // 第一级子节点
    SECOND: 2, // 第二级子节点
    THIRD: 3, // 第三级子节点
    FOURTH: 4, // 第四级子节点（第4层以后都使用此层样式）
    FIFTH: 5, // 第五级子节点
    SIXTH: 6, // 第六级子节点
    SEVENTH: 7, // 第七级子节点（最深层级）
    MAX_LEVEL: 7 // 最大支持层级
  },

  // 连接线配置 - 控制节点间连接线的样式
  CONNECTION: {
    LINE_WIDTH: 2, // rpx 连接线宽度
    COLOR: 'rgba(160,165,186,0.6)', // 连接线颜色（半透明灰色）
    EXTENSION_LENGTH: 16, // rpx 第一级节点连接线延伸长度
    HORIZONTAL_SEGMENT: 32 // rpx 水平连接线段长度
  }
};

// ==================== 节点样式常量 ====================
const NODE_STYLE_CONSTANTS = {
  // 根节点样式配置 - 树形图的起始节点
  ROOT: {
    BACKGROUND_COLOR: '#1E75DB', // 蓝色背景，突出根节点的重要性
    BORDER_RADIUS: 8, // rpx 圆角半径，使节点更美观
    FONT_SIZE: 28, // rpx 字体大小，根节点使用较大字体
    FONT_WEIGHT: 600, // 字体粗细，加粗显示
    COLOR: '#FFFFFF', // 白色文字，与蓝色背景形成对比
    WIDTH: 64, // rpx 固定宽度
    PADDING_X: 20, // rpx 水平内边距
    PADDING_Y: 20, // rpx 垂直内边距
    MIN_HEIGHT: 200 // rpx 最小高度，适应垂直文字布局
  },

  // 第一级节点样式配置 - 主要分类节点
  FIRST_LEVEL: {
    BACKGROUND_COLOR: '#FFFFFF', // 白色背景，简洁清晰
    BORDER_RADIUS: 8, // rpx 圆角半径
    FONT_SIZE: 26, // rpx 节点名称字体大小
    COUNT_FONT_SIZE: 24, // rpx 计数文字字体大小，略小于名称
    FONT_WEIGHT: 400, // 正常字体粗细
    COLOR: '#20263A', // 深色文字，主要内容颜色
    COUNT_COLOR: '#74798C', // 灰色计数文字，次要信息颜色
    PADDING: 20, // rpx 统一内边距
    TEXT_GAP: 8, // rpx 名称与计数之间的间距
    MIN_WIDTH: 112, // rpx 最小宽度
    MIN_HEIGHT: 104, // rpx 最小高度，容纳上下两行文字
    ICON_SIZE: 32, // rpx 展开/折叠图标尺寸
    ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_opens.png', // 图标路径（向后兼容）
    EXPAND_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_opens.png', // 展开状态图标路径
    COLLAPSE_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_closes.png', // 收起状态图标路径
    BORDER_COLOR: '#E5E5E5' // 边框颜色（可选）
  },

  // 第二级节点样式配置 - 次级分类节点
  SECOND_LEVEL: {
    BACKGROUND_COLOR: 'rgba(74,184,255,0.2)', // 浅蓝色背景，20%透明度
    BORDER_RADIUS: 8, // rpx 圆角半径
    FONT_SIZE: 28, // rpx 文字字体大小
    COUNT_FONT_SIZE: 28, // rpx 计数字体大小，与文字相同
    FONT_WEIGHT: 400, // 正常字体粗细
    COLOR: '#20263A', // 深色文字
    COUNT_COLOR: '#74798C', // 灰色计数文字
    PADDING_X: 20, // rpx 水平内边距
    PADDING_Y: 12, // rpx 垂直内边距，比第一级略小
    TEXT_GAP: 8, // rpx 文字间距
    MIN_WIDTH: 208, // rpx 最小宽度
    MIN_HEIGHT: 70, // rpx 最小高度
    ICON_SIZE: 28, // rpx 展开/折叠图标尺寸，比第一级略小
    EXPAND_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_opens.png', // 展开状态图标路径
    COLLAPSE_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_closes.png' // 收起状态图标路径
  },

  // 第三级节点样式配置 - 三级分类节点
  THIRD_LEVEL: {
    BACKGROUND_COLOR: 'rgba(74,184,255,0.1)', // 更浅的蓝色背景，10%透明度
    BORDER_RADIUS: 8, // rpx 圆角半径
    FONT_SIZE: 28, // rpx 文字字体大小
    COUNT_FONT_SIZE: 28, // rpx 计数字体大小
    FONT_WEIGHT: 400, // 正常字体粗细
    COLOR: '#525665', // 中等深度的文字颜色
    COUNT_COLOR: '#74798C', // 灰色计数文字
    PADDING_X: 20, // rpx 水平内边距
    PADDING_Y: 12, // rpx 垂直内边距
    TEXT_GAP: 8, // rpx 文字间距
    MIN_WIDTH: 208, // rpx 最小宽度
    MIN_HEIGHT: 70, // rpx 最小高度
    ICON_SIZE: 28, // rpx 展开/折叠图标尺寸，比第二级更小
    EXPAND_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_opens.png', // 展开状态图标路径
    COLLAPSE_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_closes.png' // 收起状态图标路径
  },

  // 第四级节点样式配置 - 第4层及以后的通用样式（支持无限层级）
  FOURTH_LEVEL: {
    BACKGROUND_COLOR: '#F2F2F2', // 浅灰色背景，表示深层级
    BORDER_RADIUS: 3, // rpx 较小的圆角半径，区别于上级节点
    FONT_SIZE: 28, // rpx 文字字体大小
    COUNT_FONT_SIZE: 28, // rpx 计数字体大小
    FONT_WEIGHT: 400, // 正常字体粗细
    COLOR: '#525665', // 中等深度的文字颜色
    COUNT_COLOR: '#74798C', // 灰色计数文字
    PADDING_X: 20, // rpx 水平内边距
    PADDING_Y: 12, // rpx 垂直内边距
    TEXT_GAP: 8, // rpx 文字间距
    MIN_WIDTH: 208, // rpx 最小宽度
    MIN_HEIGHT: 70, // rpx 最小高度
    ICON_SIZE: 28, // rpx 展开/折叠图标尺寸
    EXPAND_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_opens.png', // 展开状态图标路径
    COLLAPSE_ICON_PATH:
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_closes.png' // 收起状态图标路径
  }
};

// ==================== 性能相关常量 ====================
const PERFORMANCE_CONSTANTS = {
  // 缓存配置 - 控制组件的缓存行为以优化性能
  MAX_CACHE_SIZE: 1000, // 最大缓存条目数，防止内存泄漏
  CACHE_CLEANUP_THRESHOLD: 800, // 缓存清理阈值，达到此数量时触发清理

  // 渲染配置 - 控制渲染时机和频率
  RENDER_DELAY: 50, // ms 渲染延迟，确保DOM完全加载
  DEBOUNCE_DELAY: 100, // ms 防抖延迟，避免频繁重绘
  FRAME_RATE_LIMIT: 16, // ms 帧率限制，约60fps

  // 内存管理 - 监控和控制内存使用

  MAX_MEMORY_USAGE: 50 * 1024 * 1024, // bytes 最大内存使用量，50MB

  // 超时配置 - 防止操作无限等待
  CANVAS_INIT_TIMEOUT: 5000, // ms Canvas初始化超时
  IMAGE_LOAD_TIMEOUT: 10000 // ms 图片加载超时
};

// ==================== 事件相关常量 ====================
const EVENT_CONSTANTS = {
  // 触摸事件配置 - 控制用户交互行为
  TOUCH: {
    DRAG_THRESHOLD: 5, // px 拖拽阈值，超过此距离才认为是拖拽
    TAP_TIMEOUT: 300, // ms 点击超时，短于此时间认为是点击
    LONG_PRESS_TIMEOUT: 500 // ms 长按超时，超过此时间认为是长按
  }
};

// 默认数据配置
const DEFAULT_DATA = {
  TREE_DATA: [
    {
      name: '生物医药产业链',
      count: 22,
      children: [
        {
          name: '上游',
          count: 22,
          children: [
            {
              name: '钢铁',
              count: 22,
              children: [
                {
                  name: '原料',
                  count: 22,
                  children: [
                    {
                      name: '矿山开采',
                      count: 22,
                      children: [
                        {
                          name: '铁矿石开采',
                          count: 15,
                          children: [
                            {name: '露天开采', count: 8},
                            {name: '地下开采', count: 7}
                          ]
                        },
                        {name: '煤炭开采', count: 7}
                      ]
                    },
                    {
                      name: '冶炼加工',
                      count: 22,
                      children: [
                        {
                          name: '高炉冶炼',
                          count: 12,
                          children: [
                            {name: '炼铁工艺', count: 6},
                            {name: '炼钢工艺', count: 6}
                          ]
                        },
                        {name: '电炉冶炼', count: 10}
                      ]
                    }
                  ]
                },
                {name: '加工制造企业', count: 22}
              ]
            },
            {
              name: '电子元器件制造',
              count: 22,
              children: [
                {name: '芯片', count: 22},
                {name: '传感器设备', count: 22},
                {name: '电路板', count: 22}
              ]
            }
          ]
        },
        {
          name: '中游制造业',
          count: 22,
          children: [
            {
              name: '设备',
              count: 22,
              children: [
                {name: '机械', count: 22},
                {name: '自动化', count: 22}
              ]
            },
            {
              name: '系统集成服务平台',
              count: 22,
              children: [
                {name: '软件', count: 22},
                {name: '硬件集成方案', count: 22}
              ]
            },
            {
              name: '软件开发',
              count: 22,
              children: [{name: 'APP', count: 22}]
            }
          ]
        },
        {
          name: '下游',
          count: 22,
          children: [
            {
              name: '汽车',
              count: 22,
              children: [
                {name: '新能源', count: 22},
                {name: '传统燃油车制造', count: 22}
              ]
            },
            {
              name: '航空',
              count: 22,
              children: [{name: '民航', count: 22}]
            }
          ]
        }
      ]
    }
  ],
  CANVAS_HEIGHT: 1200, // rpx 默认Canvas高度
  CANVAS_WIDTH: 750 // rpx 默认Canvas宽度（屏幕宽度）
};

// ==================== 工具函数相关常量 ====================
const UTILS_CONSTANTS = {
  // 屏幕和单位转换相关
  DEFAULT_SCREEN_WIDTH: 375, // px 默认屏幕宽度（iPhone 6/7/8）
  RPX_BASE: 750, // rpx 基准值，微信小程序标准
  DEFAULT_DPR: 2, // 默认设备像素比

  // 文字计算相关
  CHINESE_CHAR_RATIO: 1.0, // 中文字符宽度比例
  ENGLISH_CHAR_RATIO: 0.6, // 英文字符宽度比例
  LINE_HEIGHT_RATIO: 1.2, // 行高比例

  // 节点尺寸相关
  MIN_NODE_WIDTH: 60, // px 节点最小宽度
  MIN_NODE_HEIGHT: 30 // px 节点最小高度
};

// ==================== Canvas相关常量 ====================
const CANVAS_CONSTANTS = {
  // 选择器和ID
  SELECTOR_ID: '#treeCanvas', // Canvas选择器ID
  CANVAS_ID: 'treeCanvas', // Canvas元素ID

  // 初始化相关
  INIT_DELAY: 50, // ms Canvas初始化延迟
  QUERY_TIMEOUT: 5000, // ms 查询超时时间

  // 背景色
  BACKGROUND_COLOR: '#f7f7f7' // Canvas背景色
};

// ==================== 绘制相关常量 ====================
const DRAW_CONSTANTS = {
  // 箭头配置
  ARROW_SIZE_RPX: 8, // rpx 箭头大小
  ARROW_COLOR: 'rgba(160,165,186,0.6)', // 箭头颜色
  DEFAULT_ARROW_SIZE: 4, // px 默认箭头大小

  // 箭头方向枚举
  DIRECTIONS: {
    DOWN: 'down', // 向下箭头
    RIGHT: 'right', // 向右箭头
    LEFT: 'left', // 向左箭头
    UP: 'up' // 向上箭头
  }
};

// ==================== 调试和日志相关常量 ====================
const DEBUG_CONSTANTS = {
  // 日志级别
  LOG_LEVELS: {
    ERROR: 0, // 错误日志
    WARN: 1, // 警告日志
    INFO: 2, // 信息日志
    DEBUG: 3 // 调试日志
  },

  // 调试标志
  ENABLE_PERFORMANCE_LOG: false, // 是否启用性能日志
  ENABLE_LAYOUT_LOG: false, // 是否启用布局日志
  ENABLE_RENDER_LOG: false // 是否启用渲染日志
};

// ==================== 导出相关常量 ====================
const EXPORT_CONSTANTS = {
  // 导出文件配置
  FILE_TYPE: 'png', // 导出文件格式
  QUALITY: 1, // 图片质量，1为最高质量
  PADDING: 40, // rpx 导出图片边距

  // 尺寸限制
  MAX_WIDTH: 4096, // px 最大导出宽度
  MAX_HEIGHT: 4096, // px 最大导出高度
  MIN_WIDTH: 200, // px 最小导出宽度
  MIN_HEIGHT: 200, // px 最小导出高度

  // 内存安全限制
  MAX_SAFE_AREA: 20 * 1024 * 1024, // 20M像素的绝对上限
  MEMORY_THRESHOLDS: {
    HIGH_END: 6, // GB - 高端设备内存阈值
    MID_HIGH: 4, // GB - 中高端设备内存阈值
    MID_RANGE: 3, // GB - 中端设备内存阈值
    LOW_END: 2 // GB - 低端设备内存阈值
  },
  SAFE_AREAS: {
    HIGH_END: 16 * 1024 * 1024, // 16M像素
    MID_HIGH: 8 * 1024 * 1024, // 8M像素
    MID_RANGE: 4 * 1024 * 1024, // 4M像素
    LOW_END: 2 * 1024 * 1024 // 2M像素
  },

  // 样式配置
  BACKGROUND_COLOR: '#f7f7f7', // 导出图片背景色，与canvas背景色保持一致

  // 提示文本
  LOADING_TEXT: '正在生成图片...',
  SUCCESS_TEXT: '已保存到相册',
  ERROR_TEXT: '导出失败',
  PERMISSION_ERROR_TEXT: '保存失败，请截屏保存',
  MEMORY_ERROR_TEXT: '设备内存不足，无法导出完整图片',

  // 权限相关
  PHOTO_ALBUM_SCOPE: 'scope.writePhotosAlbum'
};

// ==================== 错误消息常量 ====================
const ERROR_MESSAGES = {
  CANVAS_INIT_FAILED: 'Canvas初始化失败',
  INVALID_TREE_DATA: '无效的树形数据',
  SYSTEM_INFO_FAILED: '获取系统信息失败',
  IMAGE_LOAD_FAILED: '图片加载失败',
  RENDER_FAILED: '渲染失败',
  MEMORY_OVERFLOW: '内存使用过多',
  EXPORT_FAILED: '导出图片失败',
  EXPORT_CANVAS_CREATE_FAILED: '创建导出canvas失败',
  EXPORT_PERMISSION_DENIED: '相册权限被拒绝'
};

module.exports = {
  LAYOUT_CONSTANTS,
  NODE_STYLE_CONSTANTS,
  PERFORMANCE_CONSTANTS,
  EVENT_CONSTANTS,
  DEFAULT_DATA,
  UTILS_CONSTANTS,
  CANVAS_CONSTANTS,
  DRAW_CONSTANTS,
  DEBUG_CONSTANTS,
  EXPORT_CONSTANTS,
  ERROR_MESSAGES
};
